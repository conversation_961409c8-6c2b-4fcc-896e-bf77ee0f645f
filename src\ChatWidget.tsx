import React, { useEffect, useState, useCallback, useRef } from 'react';
import './styles/widget.css'; // Keep existing CSS for now
import type { ChatWidgetProps, Suggestion } from './types';

import { useChatSession } from './hooks/useChatSession';
import { useImageHandler } from './hooks/useImageHandler';
import { useChatMessages } from './hooks/useChatMessages';
import { useChatVisibility } from './hooks/useChatVisibility';

import FloatingChatButton from './components/FloatingChatButton';
import ChatHeader from './components/ChatWindow/ChatHeader';
import MessageList from './components/ChatWindow/MessageList';
import ImagePreviewArea from './components/ChatWindow/ImagePreviewArea';
import ChatInputArea from './components/ChatWindow/ChatInputArea';
import ExpandedImageView from './components/ChatWindow/ExpandedImageView';
//import { ERROR_MESSAGES } from './constants';


const ChatWidget: React.FC<ChatWidgetProps> = ({ apiKey, apiUrl, isPortalMode = false }) => {
  console.info('🎮 ChatWidget: Rendering with isPortalMode:', isPortalMode);
  const {
    sessionId,
    threadId,
    initialMessages,
    isLoading: isSessionLoading,
    error: sessionError,
    initializeOrRestoreSession,
    clearCurrentChatSession,
    isSessionInitialized,
  } = useChatSession(apiKey, apiUrl);

  const {
    isChatOpen,
    unreadCount,
    toggleChat,
    handleNewBotMessage,
    lastMessageTimestamp,
  } = useChatVisibility(isPortalMode); // Start open in portal mode

  const {
    selectedImages,
    previewImageObjects,
    isUploading: isImageUploading,
    imageError,
    handleFileSelect,
    removeImage,
    clearAllImages,
    uploadSelectedImages,
    triggerFileInput,
  } = useImageHandler();

  const {
    messages,
    isSending: isMessageSending,
    sendMessageError,
    sendUserMessage,
    setMessages, // Direct access to set messages if needed (e.g., for system messages)
    clearSendMessageError,
  } = useChatMessages(apiKey, sessionId, threadId, initialMessages, apiUrl);

  const [expandedImageUrl, setExpandedImageUrl] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement | null>(null);

  // Initialize session on mount
  useEffect(() => {
    initializeOrRestoreSession();
  }, [initializeOrRestoreSession]);

  // Handle new bot messages for notifications
  useEffect(() => {
    if (messages.length > 0) {
      handleNewBotMessage(messages[messages.length - 1]);
    }
  }, [messages, handleNewBotMessage]);


  const handleUserSubmit = useCallback(async (inputText: string) => {
    clearSendMessageError(); // Clear previous errors

    let attachmentUrls: string[] = [];
    if (selectedImages.length > 0) {
      if (!threadId) {
        // This case should ideally be prevented by disabling UI, but as a safeguard:
        setMessages(prev => [...prev, { type: 'bot', content: "Session not ready for uploads. Please wait or refresh.", timestamp: Date.now() }]);
        return;
      }
      attachmentUrls = await uploadSelectedImages(apiKey, threadId, apiUrl);
      if (imageError && attachmentUrls.length !== selectedImages.length) { // Partial or full upload failure
        // Error is already set by useImageHandler, just don't proceed with message if critical
        // Or, send message with successfully uploaded images
        // For now, let's assume if imageError is set, we might not want to send.
        // User can retry sending the text part or fix image issue.
         if(attachmentUrls.length === 0 && selectedImages.length > 0) { // No images uploaded, critical error
            return;
         }
      }
      clearAllImages(); // Clear previews after attempting upload
    }

    if (inputText.trim() === '' && attachmentUrls.length === 0 && selectedImages.length > 0) {
        // This case means images were selected, but upload failed, and no text.
        // Image error is already shown.
        return;
    }
    if (inputText.trim() !== '' || attachmentUrls.length > 0) {
        await sendUserMessage(inputText, attachmentUrls);
    }
  }, [
      selectedImages, 
      threadId, 
      uploadSelectedImages, 
      apiKey, 
      apiUrl, 
      sendUserMessage, 
      imageError, 
      clearAllImages, 
      clearSendMessageError,
      setMessages
    ]);

  const handleSuggestionClick = useCallback((suggestion: Suggestion) => {
    // Suggestions are usually plain text, no images directly tied to them
    handleUserSubmit(suggestion.value);
  }, [handleUserSubmit]);

  const openExpandedImage = (url: string) => setExpandedImageUrl(url);
  const closeExpandedImage = () => setExpandedImageUrl(null);

  console.info('🔍 ChatWidget state:', {
    isSessionLoading,
    isSessionInitialized,
    sessionError,
    isChatOpen,
    isPortalMode,
    messagesCount: messages.length
  });

  if (isSessionLoading && !isSessionInitialized) { // Show loading only on initial load
    console.info('🔄 ChatWidget: Showing loading state');
    return <div className="chat-loading" role="status" aria-live="polite">Initializing Chat...</div>;
  }

  if (sessionError && !isSessionInitialized) { // Show critical session error
    console.info('❌ ChatWidget: Showing error state:', sessionError);
    return <div className="chat-error" role="alert">{sessionError}</div>;
  }
  
  // Combine loading states for UI feedback
  const overallIsLoading = isMessageSending || isImageUploading || (isSessionLoading && messages.length === 0);

  console.info('🎨 ChatWidget: Rendering main UI, chat should be visible:', (isChatOpen || isPortalMode));

  return (
    <>
      {!isPortalMode && (
        <FloatingChatButton
          onClick={toggleChat}
          unreadCount={unreadCount}
          isLoading={overallIsLoading && !isChatOpen} // Show loading on button if chat is closed
          lastMessageTimestamp={lastMessageTimestamp}
        />
      )}

      {(isChatOpen || isPortalMode) && (
        <div className={`chat-container open ${isPortalMode ? 'portal-mode' : ''}`} role="complementary" aria-label="Chat window">
          <ChatHeader
            onClose={isPortalMode ? () => window.history.back() : toggleChat}
            onClearSession={clearCurrentChatSession}
            isPortalMode={isPortalMode}
          />
          <MessageList
            messages={messages}
            isLoading={isMessageSending && messages.length > 0} // Show typing dots only if messages exist
            queueLength={0} // TODO: Expose queue length from useChatMessages if needed
            onSuggestionClick={handleSuggestionClick}
            onImageClick={openExpandedImage}
            isPortalMode={isPortalMode}
          />
          
          {/* Display general errors or message sending errors */}
          {sessionError && isSessionInitialized &&  /* Non-critical session errors after init */
            <div role="alert" style={{padding: '8px', color: 'orange', textAlign: 'center', fontSize: '12px'}}>
              Warning: {sessionError}
            </div>
          }
          {sendMessageError &&
            <div role="alert" style={{padding: '8px', color: 'red', textAlign: 'center', fontSize: '12px'}}>
              {sendMessageError}
            </div>
          }

          <ImagePreviewArea
            previewImageObjects={previewImageObjects}
            onRemoveImage={removeImage}
            onClearAllImages={clearAllImages}
            onAddImageClick={() => triggerFileInput(fileInputRef)}
            onImageClick={openExpandedImage}
            imageError={imageError}
            isUploading={isImageUploading}
          />
          <ChatInputArea
            onSendMessage={handleUserSubmit}
            onAttachClick={() => triggerFileInput(fileInputRef)}
            onCameraClick={() => triggerFileInput(fileInputRef, 'environment')}
            isSending={overallIsLoading}
            hasSelectedImages={selectedImages.length > 0}
            isPortalMode={isPortalMode}
          />
        </div>
      )}

      {expandedImageUrl && (
        <ExpandedImageView imageUrl={expandedImageUrl} onClose={closeExpandedImage} />
      )}

      {/* Hidden file input */}
      <input
        type="file"
        ref={fileInputRef}
        style={{ display: 'none' }}
        onChange={handleFileSelect}
        accept="image/*"
        multiple
      />
    </>
  );
};

export default ChatWidget;