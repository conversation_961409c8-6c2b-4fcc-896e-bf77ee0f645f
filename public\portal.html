<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Portal - QuoteAI</title>
    <meta name="description" content="Get instant quotes through our AI-powered chat portal">
    <meta name="robots" content="index, follow">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/quoteai.png" />
    
    <!-- Preconnect to external domains for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:title" content="Chat Portal - QuoteAI">
    <meta property="og:description" content="Get instant quotes through our AI-powered chat portal">
    <meta property="og:image" content="/quoteai.png">
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:title" content="Chat Portal - QuoteAI">
    <meta property="twitter:description" content="Get instant quotes through our AI-powered chat portal">
    <meta property="twitter:image" content="/quoteai.png">
    
    <!-- Styles will be injected by Vite -->
    <style>
        /* Loading screen styles */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.5s ease-out;
        }
        
        .loading-content {
            text-align: center;
            color: white;
        }
        
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-text {
            font-size: 1.2rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }
        
        .loading-subtext {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        /* Hide loading screen when portal loads */
        .portal-loaded .loading-screen {
            opacity: 0;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div class="loading-screen" id="loading-screen">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <div class="loading-text">Loading Chat Portal...</div>
            <div class="loading-subtext">Connecting to your tradie</div>
        </div>
    </div>
    
    <!-- Portal Root -->
    <div id="portal-root"></div>
    
    <!-- Error Fallback (shown if JavaScript fails) -->
    <noscript>
        <div style="
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        ">
            <div style="
                text-align: center;
                background: white;
                padding: 3rem;
                border-radius: 20px;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
                max-width: 500px;
                margin: 1rem;
            ">
                <div style="font-size: 4rem; margin-bottom: 1rem;">⚠️</div>
                <h1 style="color: #374151; margin-bottom: 1rem;">JavaScript Required</h1>
                <p style="color: #6b7280; margin-bottom: 1rem; line-height: 1.6;">
                    This chat portal requires JavaScript to function properly. 
                    Please enable JavaScript in your browser and refresh the page.
                </p>
                <button onclick="window.location.reload()" style="
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border: none;
                    padding: 0.75rem 2rem;
                    border-radius: 12px;
                    font-weight: 600;
                    cursor: pointer;
                    font-size: 1rem;
                ">
                    Refresh Page
                </button>
            </div>
        </div>
    </noscript>
    
    <!-- React Dependencies -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    
    <!-- Portal Script (will be replaced with actual build output) -->
    <script type="module">
        // Hide loading screen once portal loads
        window.addEventListener('load', () => {
            setTimeout(() => {
                document.body.classList.add('portal-loaded');
                setTimeout(() => {
                    const loadingScreen = document.getElementById('loading-screen');
                    if (loadingScreen) {
                        loadingScreen.remove();
                    }
                }, 500);
            }, 1000);
        });
        
        // Error handling for failed script loads
        window.addEventListener('error', (event) => {
            console.error('Portal loading error:', event.error);
            const loadingScreen = document.getElementById('loading-screen');
            if (loadingScreen) {
                loadingScreen.innerHTML = `
                    <div class="loading-content">
                        <div style="font-size: 4rem; margin-bottom: 1rem;">⚠️</div>
                        <div class="loading-text">Failed to Load</div>
                        <div class="loading-subtext">Please refresh the page to try again</div>
                        <button onclick="window.location.reload()" style="
                            background: white;
                            color: #667eea;
                            border: 2px solid white;
                            padding: 0.75rem 2rem;
                            border-radius: 12px;
                            font-weight: 600;
                            cursor: pointer;
                            font-size: 1rem;
                            margin-top: 1rem;
                        ">
                            Refresh Page
                        </button>
                    </div>
                `;
            }
        });
    </script>
    
    <!-- The actual portal script will be injected here during build -->
    <!-- <script src="/dist/portal-chat.es.js" type="module"></script> -->
</body>
</html>
