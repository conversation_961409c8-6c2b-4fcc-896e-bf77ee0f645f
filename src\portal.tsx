import { createRoot } from 'react-dom/client';
import PortalWidget from './PortalWidget';
import { DEFAULT_API_KEY, DEFAULT_API_URL } from './constants';
import type { WidgetConfig } from './main';
import './styles/widget.css';
import './styles/portal.css';

console.info('📦 Portal module loading...');

export interface PortalConfig extends WidgetConfig {
  tradieName?: string;
}



// Extract tradie name from URL path or query parameter
const extractTradieNameFromUrl = (): string | null => {
  // First try URL path (production format: /chat/tradie-name)
  const path = window.location.pathname;
  const pathMatch = path.match(/\/chat\/([^\/]+)/);
  if (pathMatch) {
    return decodeURIComponent(pathMatch[1]);
  }

  // Fallback to query parameter (development format: ?tradie=tradie-name)
  const urlParams = new URLSearchParams(window.location.search);
  const tradieParam = urlParams.get('tradie');
  if (tradieParam) {
    return decodeURIComponent(tradieParam);
  }

  return null;
};

// Initialize portal mode
const initializePortal = () => {
  console.info('🚀 QuoteAI Portal: Starting initialization...');
  console.info('📍 Current URL:', window.location.href);

  try {
    const tradieName = extractTradieNameFromUrl();
    console.info('🏷️ Extracted tradie name:', tradieName);

    if (!tradieName) {
      console.error('❌ QuoteAI Portal: No tradie name found in URL');
      renderErrorPage('Invalid URL: Tradie name not found');
      return;
    }

    const config: PortalConfig = {
      apiUrl: window.QUOTE_AI_API_URL || DEFAULT_API_URL,
      apiKey: DEFAULT_API_KEY,
      tradieName: tradieName,
      websiteUrl: tradieName, // Use tradie name as website identifier
      customer_name: tradieName,
    };

    console.info('⚙️ Portal config:', config);

    // Store config globally for API access
    window.QUOTE_AI_CONFIG = config;

    console.info('🎯 Attempting to render portal...');
    renderPortal(config);
    console.info('✅ Portal initialization completed');
  } catch (error) {
    console.error('💥 Portal initialization failed:', error);
    renderErrorPage(`Initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

// Render the portal
const renderPortal = (config: PortalConfig) => {
  console.info('🎨 Rendering portal...');

  const container = document.getElementById('portal-root');
  if (!container) {
    console.error('❌ Portal root element not found');
    console.info('🔍 Available elements:', document.body.innerHTML.substring(0, 200) + '...');
    return;
  }

  console.info('📦 Portal container found:', container);
  console.info('🧩 Creating React root...');

  try {
    const root = createRoot(container);
    console.info('⚛️ React root created, rendering PortalWidget...');

    root.render(
      <PortalWidget
        apiKey={config.apiKey || DEFAULT_API_KEY}
        apiUrl={config.apiUrl || DEFAULT_API_URL}
        tradieName={config.tradieName || 'Unknown'}
      />
    );

    console.info('🎉 PortalWidget rendered successfully');

    // Hide loading screen after successful render
    setTimeout(() => {
      const loadingScreen = document.getElementById('loading-screen');
      if (loadingScreen) {
        console.info('🫥 Hiding loading screen...');
        document.body.classList.add('portal-loaded');
        setTimeout(() => loadingScreen.remove(), 500);
      }
    }, 100);

  } catch (error) {
    console.error('💥 Failed to render portal:', error);
    renderErrorPage(`Render failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

// Render error page
const renderErrorPage = (errorMessage: string) => {
  const container = document.getElementById('portal-root');
  if (!container) return;

  container.innerHTML = `
    <div class="portal-error">
      <div class="error-content">
        <div class="error-icon">⚠️</div>
        <h1>Oops! Something went wrong</h1>
        <p>${errorMessage}</p>
        <p>Please check the URL and try again.</p>
        <button onclick="window.location.reload()" class="retry-button">
          Retry
        </button>
      </div>
    </div>
  `;
};

// Note: Global Window interface is already declared in main.tsx

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', initializePortal);

// Export for potential external use
export { initializePortal, extractTradieNameFromUrl };
