import React, { useState, ChangeEvent, KeyboardEvent } from 'react';

interface ChatInputAreaProps {
  onSendMessage: (text: string) => void;
  onAttachClick: () => void;
  onCameraClick: () => void;
  isSending: boolean; // For disabling input/buttons
  hasSelectedImages: boolean; // To enable send button even if text is empty
  isPortalMode?: boolean;
}

const ChatInputArea: React.FC<ChatInputAreaProps> = ({
  onSendMessage,
  onAttachClick,
  onCameraClick,
  isSending,
  hasSelectedImages,
  isPortalMode = false,
}) => {
  const [inputValue, setInputValue] = useState('');

  const handleSend = () => {
    const trimmedInput = inputValue.trim();
    if (trimmedInput === '' && !hasSelectedImages) return;
    onSendMessage(trimmedInput);
    setInputValue('');
  };

  const handleKeyDown = (event: KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSend();
    }
  };

  return (
    <div className={`chat-input ${isPortalMode ? 'portal-mode' : ''}`}>
      <input
        type="text"
        value={inputValue}
        placeholder={isPortalMode ? "Type your message here..." : "Enter your message..."}
        onChange={(e: ChangeEvent<HTMLInputElement>) => setInputValue(e.target.value)}
        onKeyDown={handleKeyDown}
        disabled={isSending}
        aria-label="Chat message input"
      />
      <div className="icons">
        <button
          className="icon-button"
          data-tooltip="Attach Photo"
          aria-label="Attach photo from files"
          onClick={onAttachClick}
          disabled={isSending}
        >
          📎
        </button>
        <button
          className="icon-button"
          data-tooltip="Take Photo"
          aria-label="Take photo with camera"
          onClick={onCameraClick}
          disabled={isSending}
        >
          📷
        </button>
        <button
          className="icon-button send-button"
          data-tooltip="Send Message"
          aria-label="Send message"
          onClick={handleSend}
          disabled={isSending || (inputValue.trim() === '' && !hasSelectedImages)}
        >
          ➤
        </button>
      </div>
    </div>
  );
};

export default ChatInputArea;