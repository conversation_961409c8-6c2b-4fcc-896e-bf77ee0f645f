<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>Chat Portal - QuoteAI</title>
    <meta name="description" content="Get instant quotes through our AI-powered chat portal">

    <!-- Mobile app-like experience -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="QuoteAI Chat">
    <meta name="theme-color" content="#667eea">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/quoteai.png" />
    
    <!-- Preconnect to external domains for performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <style>
        /* Loading screen styles */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.5s ease-out;
        }
        
        .loading-content {
            text-align: center;
            color: white;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        }
        
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-text {
            font-size: 1.2rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }
        
        .loading-subtext {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        /* Hide loading screen when portal loads */
        .portal-loaded .loading-screen {
            opacity: 0;
            pointer-events: none;
        }
        
        /* Development banner for localhost */
        .dev-banner {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: #f59e0b;
            color: white;
            padding: 0.5rem;
            text-align: center;
            font-size: 0.875rem;
            font-weight: 500;
            z-index: 10000;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            display: none;
        }
        
        .dev-banner.show {
            display: block;
        }
        
        .dev-banner a {
            color: white;
            text-decoration: underline;
            margin-left: 1rem;
        }
        
        /* Adjust portal container for dev banner */
        body.dev-mode #portal-root {
            margin-top: 40px;
            height: calc(100vh - 40px);
        }

        /* Mobile-specific optimizations */
        @media (max-width: 768px) {
            .loading-text {
                font-size: 1.1rem;
            }

            .loading-subtext {
                font-size: 0.85rem;
            }

            .loading-spinner {
                width: 40px;
                height: 40px;
            }
        }

        /* Prevent zoom on input focus for iOS */
        @media screen and (max-width: 768px) {
            input[type="text"], input[type="email"], input[type="password"], textarea {
                font-size: 16px !important;
            }
        }

        /* Safe area handling for devices with notches */
        body {
            padding-top: env(safe-area-inset-top);
            padding-bottom: env(safe-area-inset-bottom);
            padding-left: env(safe-area-inset-left);
            padding-right: env(safe-area-inset-right);
        }

        /* Smooth scrolling for better mobile experience */
        html {
            scroll-behavior: smooth;
            -webkit-text-size-adjust: 100%;
        }

        /* Prevent horizontal scrolling */
        body, html {
            overflow-x: hidden;
        }
    </style>
</head>
<body>
    <!-- Development Banner (only shown on localhost) -->
    <div class="dev-banner" id="dev-banner">
        🚧 Development Mode - Portal Testing
        <a href="/portal-test.html" target="_blank">Test Environment</a>
        <a href="javascript:location.reload()">Reload</a>
    </div>
    
    <!-- Loading Screen -->
    <div class="loading-screen" id="loading-screen">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <div class="loading-text">Loading Chat Portal...</div>
            <div class="loading-subtext" id="loading-tradie">Connecting...</div>
        </div>
    </div>
    
    <!-- Portal Root -->
    <div id="portal-root"></div>
    
    <!-- Error Fallback -->
    <noscript>
        <div style="
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        ">
            <div style="
                text-align: center;
                background: white;
                padding: 3rem;
                border-radius: 20px;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
                max-width: 500px;
                margin: 1rem;
            ">
                <div style="font-size: 4rem; margin-bottom: 1rem;">⚠️</div>
                <h1 style="color: #374151; margin-bottom: 1rem;">JavaScript Required</h1>
                <p style="color: #6b7280; margin-bottom: 1rem; line-height: 1.6;">
                    This chat portal requires JavaScript to function properly. 
                    Please enable JavaScript in your browser and refresh the page.
                </p>
                <button onclick="window.location.reload()" style="
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border: none;
                    padding: 0.75rem 2rem;
                    border-radius: 12px;
                    font-weight: 600;
                    cursor: pointer;
                    font-size: 1rem;
                ">
                    Refresh Page
                </button>
            </div>
        </div>
    </noscript>
    
    <!-- React Dependencies -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    
    <!-- Portal Script -->
    <script type="module">
        // Check if we're in development mode
        const isDev = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';

        if (isDev) {
            // In development, load the source file through Vite
            import('/src/portal.tsx').catch(error => {
                console.error('Failed to load portal module:', error);
                // Show error in loading screen
                const loadingScreen = document.getElementById('loading-screen');
                if (loadingScreen) {
                    loadingScreen.innerHTML = `
                        <div class="loading-content">
                            <div style="font-size: 4rem; margin-bottom: 1rem;">⚠️</div>
                            <div class="loading-text">Development Error</div>
                            <div class="loading-subtext">Failed to load portal module. Make sure the dev server is running.</div>
                            <button onclick="window.location.reload()" style="
                                background: white;
                                color: #667eea;
                                border: 2px solid white;
                                padding: 0.75rem 2rem;
                                border-radius: 12px;
                                font-weight: 600;
                                cursor: pointer;
                                font-size: 1rem;
                                margin-top: 1rem;
                            ">
                                Refresh Page
                            </button>
                        </div>
                    `;
                }
            });
        } else {
            // In production, load the built file
            import('/dist/portal-chat.es.js').catch(error => {
                console.error('Failed to load portal module:', error);
                // Show error in loading screen
                const loadingScreen = document.getElementById('loading-screen');
                if (loadingScreen) {
                    loadingScreen.innerHTML = `
                        <div class="loading-content">
                            <div style="font-size: 4rem; margin-bottom: 1rem;">⚠️</div>
                            <div class="loading-text">Loading Error</div>
                            <div class="loading-subtext">Failed to load portal. Please refresh the page.</div>
                            <button onclick="window.location.reload()" style="
                                background: white;
                                color: #667eea;
                                border: 2px solid white;
                                padding: 0.75rem 2rem;
                                border-radius: 12px;
                                font-weight: 600;
                                cursor: pointer;
                                font-size: 1rem;
                                margin-top: 1rem;
                            ">
                                Refresh Page
                            </button>
                        </div>
                    `;
                }
            });
        }
    </script>
    
    <script>
        // Check if we're in development mode
        const isDev = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
        
        if (isDev) {
            document.body.classList.add('dev-mode');
            document.getElementById('dev-banner').classList.add('show');
        }
        
        // Extract tradie name from URL and update loading text
        const extractTradieFromUrl = () => {
            // First try URL path (production format: /chat/tradie-name)
            const path = window.location.pathname;
            const pathMatch = path.match(/\/chat\/([^\/]+)/);
            if (pathMatch) {
                return decodeURIComponent(pathMatch[1]);
            }

            // Fallback to query parameter (development format: ?tradie=tradie-name)
            const urlParams = new URLSearchParams(window.location.search);
            const tradieParam = urlParams.get('tradie');
            if (tradieParam) {
                return decodeURIComponent(tradieParam);
            }

            return null;
        };
        
        // Update page title and loading text with tradie name
        const tradieName = extractTradieFromUrl();
        if (tradieName) {
            document.title = `Chat with ${tradieName} - QuoteAI Portal`;
            document.getElementById('loading-tradie').textContent = `Connecting to ${tradieName}...`;
        }
        
        // Hide loading screen if portal fails to load after timeout
        setTimeout(() => {
            const loadingScreen = document.getElementById('loading-screen');
            if (loadingScreen && !document.body.classList.contains('portal-loaded')) {
                console.warn('⏰ Portal loading timeout - showing fallback error');
                loadingScreen.innerHTML = `
                    <div class="loading-content">
                        <div style="font-size: 4rem; margin-bottom: 1rem;">⏰</div>
                        <div class="loading-text">Portal Loading Timeout</div>
                        <div class="loading-subtext">The portal is taking longer than expected to load</div>
                        <button onclick="window.location.reload()" style="
                            background: white;
                            color: #667eea;
                            border: 2px solid white;
                            padding: 0.75rem 2rem;
                            border-radius: 12px;
                            font-weight: 600;
                            cursor: pointer;
                            font-size: 1rem;
                            margin-top: 1rem;
                        ">
                            Refresh Page
                        </button>
                    </div>
                `;
            }
        }, 10000); // 10 second timeout
        
        // Error handling
        window.addEventListener('error', (event) => {
            console.error('Portal loading error:', event.error);
            const loadingScreen = document.getElementById('loading-screen');
            if (loadingScreen) {
                loadingScreen.innerHTML = `
                    <div class="loading-content">
                        <div style="font-size: 4rem; margin-bottom: 1rem;">⚠️</div>
                        <div class="loading-text">Failed to Load Portal</div>
                        <div class="loading-subtext">${event.error?.message || 'Please refresh the page to try again'}</div>
                        <button onclick="window.location.reload()" style="
                            background: white;
                            color: #667eea;
                            border: 2px solid white;
                            padding: 0.75rem 2rem;
                            border-radius: 12px;
                            font-weight: 600;
                            cursor: pointer;
                            font-size: 1rem;
                            margin-top: 1rem;
                        ">
                            Refresh Page
                        </button>
                        ${isDev ? `
                        <a href="/portal-test.html" style="
                            display: inline-block;
                            color: white;
                            text-decoration: underline;
                            margin-top: 1rem;
                            margin-left: 1rem;
                        ">
                            Test Environment
                        </a>
                        ` : ''}
                    </div>
                `;
            }
        });
        
        // Development helpers
        if (isDev) {
            console.log('🚧 Portal Development Mode');
            console.log('Tradie Name:', tradieName || 'Not found in URL');
            console.log('URL Path:', window.location.pathname);
            
            // Add development keyboard shortcuts
            document.addEventListener('keydown', (e) => {
                if (e.ctrlKey || e.metaKey) {
                    switch (e.key) {
                        case 't':
                            // Ctrl/Cmd + T - Open test environment
                            e.preventDefault();
                            window.open('/portal-test.html', '_blank');
                            break;
                        case 'd':
                            // Ctrl/Cmd + D - Toggle debug info
                            e.preventDefault();
                            console.log('Debug Info:', {
                                tradieName,
                                url: window.location.href,
                                config: window.QUOTE_AI_CONFIG
                            });
                            break;
                    }
                }
            });
        }
    </script>
</body>
</html>
