import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';
import { visualizer } from 'rollup-plugin-visualizer';

export default defineConfig({
  plugins: [react()],
  build: {
    rollupOptions: {
      input: {
        widget: 'src/main.tsx',
        portal: 'src/portal.tsx',
      },
      output: {
        entryFileNames: (chunkInfo) => {
          if (chunkInfo.name === 'widget') {
            return 'chatbot-widget.[format].js';
          }
          if (chunkInfo.name === 'portal') {
            return 'portal-chat.[format].js';
          }
          return '[name].[format].js';
        },
        globals: {
          react: 'React',
          'react-dom': 'ReactDOM',
        },
        inlineDynamicImports: false,
      },
      plugins: [visualizer({ open: true })],
      external: [
        'react',
        'react-dom',
        'firebase',
        'firebase/app',
        'firebase/auth',
        'firebase/firestore',
        'firebase/storage',
        'firebase/analytics',
        '@firebase/analytics',
        'react-chatbot-kit',
        /^react-chatbot-kit\/.*/,
        /^firebase\/.*/,
        /^@firebase\/.*/
      ],
    },
    // This ensures that static assets in src/styles are copied to the dist folder
    assetsDir: 'styles',
    outDir: 'dist', // Make sure this is where your build output goes
    // Ensure proper minification and transpilation
    minify: true,
    target: 'es2015',
    reportCompressedSize: true,
    chunkSizeWarningLimit: 500,
    sourcemap: true,
  },
  server: {
    port: 5173,
    open: 'public/example-client.html', // Opens this file by default in dev server
    cors: true,
    // Serve static files from the 'public' directory during development
    fs: {
      allow: ['..'], // Allows serving files from a parent directory
    },
  },
  resolve: {
    alias: {
      // If you have any aliases or need to point to specific directories
      '@': resolve(__dirname, 'src'),
    }
  },
  // This tells Vite to serve files from the public directory as if they were in the root
  publicDir: 'public',
  optimizeDeps: {
    exclude: [
      'firebase',
      '@firebase/analytics',
      'react-chatbot-kit'
    ],
  },
  logLevel: 'info',
});
