import React, { useState } from 'react';
import ChatWidget from './ChatWidget';
import MockChatWidget from './MockChatWidget';

interface PortalWidgetProps {
  apiKey: string;
  apiUrl: string;
  tradieName: string;
}

const PortalWidget: React.FC<PortalWidgetProps> = ({ apiKey, apiUrl, tradieName }) => {
  console.info('🎭 PortalWidget: Rendering with props:', { apiKey: apiKey?.substring(0, 10) + '...', apiUrl, tradieName });

  const [showChatWidget, setShowChatWidget] = useState(true);
  const [showDebugInfo, setShowDebugInfo] = useState(false);
  const [useMockWidget, setUseMockWidget] = useState(true);

  return (
    <div className="portal-container">
      <div className="portal-header">
        <div className="portal-branding">
          <div className="portal-logo">💬</div>
          <div className="portal-title">
            <h1>Chat with {tradieName}</h1>
            <p>Get your free quote today!</p>
          </div>
        </div>
        <div className="portal-status">
          <div className="connection-status online">
            <span className="status-indicator"></span>
            Online
          </div>
          <button
            onClick={() => setShowDebugInfo(!showDebugInfo)}
            style={{
              marginLeft: '1rem',
              padding: '0.25rem 0.5rem',
              fontSize: '0.75rem',
              background: 'rgba(255,255,255,0.2)',
              border: '1px solid rgba(255,255,255,0.3)',
              borderRadius: '4px',
              color: 'white',
              cursor: 'pointer'
            }}
          >
            Debug
          </button>
        </div>
      </div>

      {showDebugInfo && (
        <div style={{
          background: '#f0f0f0',
          padding: '1rem',
          margin: '1rem',
          borderRadius: '8px',
          fontSize: '0.875rem',
          fontFamily: 'monospace'
        }}>
          <h4>Debug Information:</h4>
          <p><strong>Tradie Name:</strong> {tradieName}</p>
          <p><strong>API Key:</strong> {apiKey?.substring(0, 10)}...</p>
          <p><strong>API URL:</strong> {apiUrl}</p>
          <p><strong>Show Chat Widget:</strong> {showChatWidget ? 'Yes' : 'No'}</p>
          <p><strong>Use Mock Widget:</strong> {useMockWidget ? 'Yes (No Backend)' : 'No (Real Backend)'}</p>
          <div style={{ display: 'flex', gap: '0.5rem', marginTop: '0.5rem' }}>
            <button
              onClick={() => setShowChatWidget(!showChatWidget)}
              style={{
                padding: '0.5rem 1rem',
                background: '#007bff',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              {showChatWidget ? 'Hide' : 'Show'} Chat Widget
            </button>
            <button
              onClick={() => setUseMockWidget(!useMockWidget)}
              style={{
                padding: '0.5rem 1rem',
                background: useMockWidget ? '#28a745' : '#ffc107',
                color: useMockWidget ? 'white' : 'black',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Use {useMockWidget ? 'Real' : 'Mock'} Widget
            </button>
          </div>
        </div>
      )}

      <div className="portal-chat-area">
        {showChatWidget ? (
          useMockWidget ? (
            <MockChatWidget
              apiKey={apiKey}
              apiUrl={apiUrl}
              isPortalMode={true}
            />
          ) : (
            <ChatWidget
              apiKey={apiKey}
              apiUrl={apiUrl}
              isPortalMode={true}
            />
          )
        ) : (
          <div style={{
            padding: '2rem',
            textAlign: 'center',
            background: '#f8fafc',
            flex: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <div>
              <h2>Chat Widget Hidden</h2>
              <p>Use the debug panel above to show the chat widget.</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PortalWidget;
